#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> để detect SQL Server driver name và tạo connection string phù hợp
"""

import pyodbc
import os
from dotenv import load_dotenv

load_dotenv()

def detect_sql_driver():
    """Detect available SQL Server driver"""
    drivers = pyodbc.drivers()
    print("Available ODBC drivers:")
    for driver in drivers:
        print(f"  - {driver}")
    
    # Ưu tiên sử dụng ODBC Driver 18, fallback về SQL Server
    preferred_drivers = [
        "ODBC Driver 18 for SQL Server",
        "ODBC Driver 17 for SQL Server", 
        "SQL Server"
    ]
    
    for preferred in preferred_drivers:
        if preferred in drivers:
            print(f"\n✅ Using driver: {preferred}")
            return preferred
    
    print("\n❌ No SQL Server driver found!")
    return None

def create_connection_string(driver_name):
    """Create connection string with detected driver"""
    base_conn_str = os.getenv('SQL_CONNECTION_STRING', '')
    
    if not base_conn_str:
        print("❌ No SQL_CONNECTION_STRING found in .env")
        return None
    
    # Replace driver name in connection string
    if "Driver={" in base_conn_str:
        # Extract everything after the first Driver={...}; part
        parts = base_conn_str.split(';', 1)
        if len(parts) > 1:
            new_conn_str = f"Driver={{{driver_name}}};{parts[1]}"
            print(f"\n📝 New connection string: {new_conn_str[:80]}...")
            return new_conn_str
    
    print("❌ Cannot parse connection string")
    return None

def test_connection(conn_str):
    """Test SQL connection"""
    try:
        import pyodbc
        conn = pyodbc.connect(conn_str, timeout=5)
        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        conn.close()
        print(f"✅ Connection successful! Test result: {result[0]}")
        return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def main():
    print("SQL DRIVER DETECTION AND CONNECTION TEST")
    print("=" * 50)
    
    # Detect driver
    driver_name = detect_sql_driver()
    if not driver_name:
        return
    
    # Create connection string
    conn_str = create_connection_string(driver_name)
    if not conn_str:
        return
    
    # Test connection
    print(f"\n🔍 Testing connection...")
    success = test_connection(conn_str)
    
    if success:
        print(f"\n🎉 SUCCESS! Use this driver name: {driver_name}")
        print(f"📋 Full connection string:")
        print(f"   {conn_str}")
    else:
        print(f"\n❌ FAILED! Driver '{driver_name}' doesn't work")

if __name__ == "__main__":
    main()
