#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test UI improvements - SQL table name fix and better list results
"""

import requests
import time
import sys
import os
from dotenv import load_dotenv

# Set encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# Load environment variables
load_dotenv()

def test_sql_ui_fix():
    """Test SQL UI with problematic table name"""
    base_url = "http://127.0.0.1:5000"
    
    print("TESTING SQL UI FIX")
    print("=" * 30)
    
    # Test with problematic table name "table"
    print("\n--- Testing SQL with 'table' as table name ---")
    data = {
        'service': 'sql',
        'action': 'add',
        'sql_table': 'table',  # This should be fixed now
        'sql_value': '123'
    }
    
    try:
        response = requests.post(base_url, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            if "successfully" in response.text.lower():
                print("✅ SQL with 'table' name successful")
            elif "error" in response.text.lower():
                print("❌ SQL with 'table' name failed")
                # Look for specific error
                if "Incorrect syntax near the keyword 'table'" in response.text:
                    print("❌ Still has the original SQL syntax error")
                else:
                    print("❌ Different error occurred")
            else:
                print("⚠️ SQL result unclear")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    return True

def test_list_improvements():
    """Test improved list functionality for all services"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n\nTESTING LIST IMPROVEMENTS")
    print("=" * 40)
    
    services = ['sql', 'cosmos', 'blob', 'acr', 'redis']
    
    for service in services:
        print(f"\n--- Testing {service.upper()} list ---")
        data = {
            'service': service,
            'action': 'list'
        }
        
        try:
            response = requests.post(base_url, data=data, timeout=30)
            if response.status_code == 200:
                # Check for improved content
                if "connection successful" in response.text.lower():
                    # Look for additional information
                    if "tip:" in response.text.lower() or "💡" in response.text:
                        print(f"✅ {service.upper()} list shows tips/guidance")
                    elif service == 'sql' and ("table:" in response.text.lower() or "📋" in response.text):
                        print(f"✅ {service.upper()} list shows table information")
                    elif service == 'blob' and ("container:" in response.text.lower() or "📦" in response.text):
                        print(f"✅ {service.upper()} list shows container information")
                    elif service == 'redis' and ("key:" in response.text.lower() or "🔑" in response.text):
                        print(f"✅ {service.upper()} list shows key information")
                    elif service == 'acr' and ("login server:" in response.text.lower() or "🔗" in response.text):
                        print(f"✅ {service.upper()} list shows registry information")
                    else:
                        print(f"⚠️ {service.upper()} list basic success but no detailed info")
                else:
                    print(f"❌ {service.upper()} list failed")
            else:
                print(f"❌ {service.upper()} HTTP error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {service.upper()} request failed: {e}")

def wait_for_server():
    """Wait for Flask server to be ready"""
    base_url = "http://127.0.0.1:5000"
    max_attempts = 10
    
    for i in range(max_attempts):
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print(f"✅ Server is ready after {i+1} attempts")
                return True
        except:
            pass
        
        print(f"Waiting for server... attempt {i+1}/{max_attempts}")
        time.sleep(2)
    
    print("❌ Server not ready after maximum attempts")
    return False

if __name__ == "__main__":
    print("Waiting for Flask server to start...")
    if wait_for_server():
        test_sql_ui_fix()
        test_list_improvements()
        print("\n" + "=" * 50)
        print("UI IMPROVEMENTS TEST COMPLETED")
    else:
        print("❌ Cannot connect to Flask server")
        sys.exit(1)
