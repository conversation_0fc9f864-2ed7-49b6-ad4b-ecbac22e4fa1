# Script để download và cài đặt ODBC Driver 18 for SQL Server
Write-Host "Downloading ODBC Driver 18 for SQL Server..." -ForegroundColor Green

# URL download cho ODBC Driver 18
$downloadUrl = "https://go.microsoft.com/fwlink/?linkid=2249006"
$installerPath = "$env:TEMP\msodbcsql.msi"

try {
    # Download installer
    Write-Host "Downloading from: $downloadUrl" -ForegroundColor Yellow
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "Download completed: $installerPath" -ForegroundColor Green
    
    # Cài đặt silent mode
    Write-Host "Installing ODBC Driver 18..." -ForegroundColor Yellow
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $installerPath, "/quiet", "IACCEPTMSODBCSQLLICENSETERMS=YES" -Wait
    
    Write-Host "Installation completed!" -ForegroundColor Green
    
    # Kiểm tra cài đặt
    Write-Host "Checking installed drivers..." -ForegroundColor Yellow
    $drivers = Get-OdbcDriver | Where-Object {$_.Name -like "*SQL Server*"}
    if ($drivers) {
        Write-Host "✅ ODBC drivers found:" -ForegroundColor Green
        $drivers | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Cyan }
    } else {
        Write-Host "❌ No SQL Server ODBC drivers found" -ForegroundColor Red
    }
    
    # Cleanup
    Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Done!" -ForegroundColor Green
