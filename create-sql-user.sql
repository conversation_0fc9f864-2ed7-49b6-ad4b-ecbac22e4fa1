-- Script để tạo contained user cho AKS Managed Identity
-- Chạy script này với SQL admin credentials

-- Tạo user cho AKS system-assigned managed identity
CREATE USER [primary-sabeco-aks] FROM EXTERNAL PROVIDER;

-- <PERSON><PERSON><PERSON> quyền cần thiết
ALTER ROLE db_datareader ADD MEMBER [primary-sabeco-aks];
ALTER ROLE db_datawriter ADD MEMBER [primary-sabeco-aks];
ALTER ROLE db_ddladmin ADD MEMBER [primary-sabeco-aks];

-- Hoặc tạo user cho user-assigned managed identity đã tạo
CREATE USER [sabeco-demo-app-identity] FROM EXTERNAL PROVIDER;
ALTER ROLE db_datareader ADD MEMBER [sabeco-demo-app-identity];
ALTER ROLE db_datawriter ADD MEMBER [sabeco-demo-app-identity];
ALTER ROLE db_ddladmin ADD MEMBER [sabeco-demo-app-identity];

-- <PERSON><PERSON><PERSON> tra users đã tạo
SELECT name, type_desc, authentication_type_desc 
FROM sys.database_principals 
WHERE type IN ('E', 'X');
