#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test for SQL Database connection
"""

import os
from dotenv import load_dotenv
from app import test_azure_sql_full

# Load environment variables
load_dotenv()

def main():
    conn_str = os.getenv('SQL_CONNECTION_STRING')
    if not conn_str:
        print("❌ No SQL_CONNECTION_STRING found")
        return
    
    print("Testing SQL Database connection...")
    print(f"Connection string: {conn_str[:50]}...")
    
    try:
        steps = test_azure_sql_full(conn_str)
        print("\nResults:")
        for success, msg in steps:
            status = "✅" if success else "❌"
            print(f"  {status} {msg}")
        
        success_count = sum(1 for success, _ in steps if success)
        total_count = len(steps)
        print(f"\nSummary: {success_count}/{total_count} steps successful")
        
        if success_count == total_count:
            print("🎉 SQL Database connection SUCCESSFUL!")
        else:
            print("❌ SQL Database connection FAILED!")
            
    except Exception as e:
        print(f"❌ Error testing SQL: {e}")

if __name__ == "__main__":
    main()
