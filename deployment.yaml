apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-app-sabeco
  labels:
    app: demo-app-sabeco
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-app-sabeco
  template:
    metadata:
      labels:
        app: demo-app-sabeco
        aadpodidbinding: sabeco-demo-app-identity
    spec:
      serviceAccountName: sabeco-demo-app-sa
      containers:
      - name: sabeco-demo-app
        image: primarysabecocontainer.azurecr.io/sabeco-demo-app:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 5000
          name: http
        env:
        - name: AZURE_TENANT_ID
          valueFrom:
            secretKeyRef:
              name: azure-credentials
              key: AZURE_TENANT_ID
        - name: SQL_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: azure-credentials
              key: SQL_CONNECTION_STRING
        - name: SQL_SERVER
          valueFrom:
            secretKeyRef:
              name: azure-credentials
              key: SQL_SERVER
        - name: SQL_DATABASE
          valueFrom:
            secretKeyRef:
              name: azure-credentials
              key: SQL_DATABASE
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /
            port: 5000
          initialDelaySeconds: 10
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /
            port: 5000
          initialDelaySeconds: 20
          periodSeconds: 20

