apiVersion: v1
kind: Secret
metadata:
  name: azure-credentials
  namespace: default
type: Opaque
stringData:
  # Sử dụng Managed Identity - không cần client ID
  AZURE_TENANT_ID: "2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0"

  # SQL connection với SQL authentication (sử dụng username/password) - Container Linux
  SQL_CONNECTION_STRING: "Driver={ODBC Driver 18 for SQL Server};Server=tcp:primary-sabeco-sql-server.database.windows.net,1433;Database=primary-sabeco-sql-db;Uid=lvthinh;Pwd=Tatn25ct10lt@;Encrypt=yes;TrustServerCertificate=yes;Connection Timeout=30;"

  # Hoặc sử dụng cấu hình riêng biệt
  SQL_SERVER: "primary-sabeco-sql-server.database.windows.net"
  SQL_DATABASE: "primary-sabeco-sql-db"
