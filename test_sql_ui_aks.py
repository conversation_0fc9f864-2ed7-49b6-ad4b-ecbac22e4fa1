#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test SQL Database UI functionality on AKS
"""

import requests
import time

def test_sql_list_function():
    """Test SQL Database list function via web UI"""
    base_url = "http://**************"
    
    print("TESTING SQL DATABASE LIST FUNCTION ON AKS")
    print("=" * 50)
    
    # Test SQL list function
    print("\n--- Testing SQL Database List ---")
    data = {
        'service': 'sql',
        'action': 'list'
    }
    
    try:
        print(f"Sending POST request to {base_url}...")
        response = requests.post(base_url, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            # Check for ODBC error
            if "IM002" in response.text:
                print("❌ ODBC Driver error still present!")
                print("Error details:")
                # Extract error message
                lines = response.text.split('\n')
                for line in lines:
                    if "IM002" in line or "Data source name not found" in line:
                        print(f"  {line.strip()}")
                return False
            elif "successfully" in response.text.lower() or "table:" in response.text.lower():
                print("✅ SQL Database list function successful!")
                # Extract table list
                lines = response.text.split('\n')
                for line in lines:
                    if "Table:" in line:
                        print(f"  Found: {line.strip()}")
                return True
            elif "error" in response.text.lower():
                print("❌ SQL Database list function failed with other error")
                # Look for specific error
                lines = response.text.split('\n')
                for line in lines:
                    if "error" in line.lower():
                        print(f"  Error: {line.strip()}")
                return False
            else:
                print("⚠️ SQL result unclear")
                print("Response preview:")
                print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_sql_add_function():
    """Test SQL Database add function via web UI"""
    print("\n--- Testing SQL Database Add ---")
    data = {
        'service': 'sql',
        'action': 'add',
        'sql_table': 'test_table',
        'sql_value': 'test_value_123'
    }
    
    try:
        base_url = "http://**************"
        print(f"Sending POST request to {base_url}...")
        response = requests.post(base_url, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            if "IM002" in response.text:
                print("❌ ODBC Driver error still present!")
                return False
            elif "successfully" in response.text.lower():
                print("✅ SQL Database add function successful!")
                return True
            elif "error" in response.text.lower():
                print("❌ SQL Database add function failed")
                return False
            else:
                print("⚠️ SQL add result unclear")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    print("Waiting for AKS service to be ready...")
    time.sleep(2)
    
    # Test list function (this was failing before)
    list_success = test_sql_list_function()
    
    # Test add function
    add_success = test_sql_add_function()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"  SQL List Function: {'✅ PASS' if list_success else '❌ FAIL'}")
    print(f"  SQL Add Function: {'✅ PASS' if add_success else '❌ FAIL'}")
    
    if list_success and add_success:
        print("\n🎉 ALL SQL DATABASE UI FUNCTIONS WORKING!")
        print("✅ ODBC Driver issue has been resolved!")
    else:
        print("\n❌ Some SQL Database UI functions still failing")
        print("❌ ODBC Driver issue may still exist")

if __name__ == "__main__":
    main()
